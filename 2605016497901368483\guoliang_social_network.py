import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_guoliang_network():
    """创建国梁的人物社会关系网络图"""
    
    G = nx.Graph()
    
    # 人物信息
    characters = {
        '国梁': {
            'type': 'core', 
            'position': (0, 0), 
            'size': 5000, 
            'label': '国梁\n(隆吉/丹中/笠民)',
            'description': '满洲正黄旗，迪化同知'
        },
        '伍弥泰': {
            'type': 'superior', 
            'position': (-3, 2.5), 
            'size': 4000, 
            'label': '伍弥泰\n(诚毅伯)',
            'description': '乌鲁木齐办事大臣，国梁的直接上级'
        },
        '李虹山': {
            'type': 'poet_friend', 
            'position': (3, 2.5), 
            'size': 3000, 
            'label': '李虹山',
            'description': '诗歌唱和者，生平不详'
        },
        '妻孥': {
            'type': 'family', 
            'position': (0, -3), 
            'size': 2500, 
            'label': '妻孥\n(留居京城)',
            'description': '国梁的妻子和子女'
        },
        '西域各族民众': {
            'type': 'social_group', 
            'position': (-3, -1.5), 
            'size': 3500, 
            'label': '西域各族民众\n(缠头/回女/华人)',
            'description': '国梁治理和观察的对象'
        }
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        ('国梁', '伍弥泰', {
            'weight': 4, 
            'relation': '上级/诗歌唱和',
            'evidence': '《雨后之东山次诚毅伯韵》',
            'description': '工作上级关系 + 文人诗歌交流',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('国梁', '李虹山', {
            'weight': 3, 
            'relation': '诗歌唱和',
            'evidence': '《中元次日夜半听雨，次李虹山韵》',
            'description': '文学交往，诗歌酬答',
            'color': '#3498db',
            'style': 'solid'
        }),
        ('国梁', '妻孥', {
            'weight': 5, 
            'relation': '直系亲属',
            'evidence': '遣妻孥旋都，只身赴迪化同知任',
            'description': '家庭关系，分居两地',
            'color': '#9b59b6',
            'style': 'solid'
        }),
        ('国梁', '西域各族民众', {
            'weight': 2, 
            'relation': '观察/治理对象',
            'evidence': '诗中描写各族风俗',
            'description': '地方官与治理对象的关系',
            'color': '#27ae60',
            'style': 'dashed'
        })
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_guoliang_graph(G, characters):
    """绘制国梁网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',          # 红色 - 核心人物
        'superior': '#3498db',      # 蓝色 - 上级
        'poet_friend': '#f39c12',   # 橙色 - 诗友
        'family': '#9b59b6',        # 紫色 - 家庭
        'social_group': '#27ae60'   # 绿色 - 社会群体
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系类型使用不同样式
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        style = data['style']
        
        # 绘制边的光晕效果
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2,
                              alpha=0.3, 
                              edge_color=color,
                              style=style)
        
        # 绘制主边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 1.2,
                              alpha=0.8, 
                              edge_color=color,
                              style=style)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点光晕
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size + 300,
                             alpha=0.3)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小和颜色
        if data['type'] == 'core':
            fontsize = 14
            bbox_color = '#2c3e50'
        elif data['type'] == 'social_group':
            fontsize = 10
            bbox_color = '#34495e'
        else:
            fontsize = 11
            bbox_color = '#34495e'
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.4", 
                            facecolor=bbox_color, 
                            alpha=0.7,
                            edgecolor='none'))
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物（国梁）', alpha=0.9),
        mpatches.Patch(color=node_colors['superior'], label='上级/诗歌唱和者（伍弥泰）', alpha=0.9),
        mpatches.Patch(color=node_colors['poet_friend'], label='诗歌唱和者（李虹山）', alpha=0.9),
        mpatches.Patch(color=node_colors['family'], label='家庭成员（妻孥）', alpha=0.9),
        mpatches.Patch(color=node_colors['social_group'], label='社会群体（各族民众）', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=11,
             title='人物关系类型',
             title_fontsize=12)
    
    # 标题
    ax.text(0.5, 0.95, "国梁人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '乾隆三十年至三十三年 迪化同知任期 (1765-1768)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-5, 5)
    ax.set_ylim(-4, 4)
    
    plt.tight_layout()
    return fig

def add_network_analysis():
    """添加网络分析"""
    
    analysis = {
        "网络特点": [
            "• 多元化关系：包含工作、文学、家庭、社会治理等多个维度",
            "• 地域分离：家庭在京城，工作在西域，体现了清代官员的流动性",
            "• 文人特色：与上级和同僚都有诗歌唱和，体现了文人官员的特点",
            "• 社会责任：作为地方官对各族民众的观察和治理"
        ],
        
        "关系分析": {
            "伍弥泰": [
                "• 双重关系：既是工作上的直接上级，又是诗歌唱和的文友",
                "• 地位重要：乌鲁木齐办事大臣，封诚毅伯",
                "• 文学交流：《雨后之东山次诚毅伯韵》体现了诗歌酬答",
                "• 关系和谐：上下级关系中融入了文人雅趣"
            ],
            "李虹山": [
                "• 纯粹的文学关系：诗歌唱和者",
                "• 身份不详：可能是当地文人或同僚",
                "• 文学证据：《中元次日夜半听雨，次李虹山韵》",
                "• 体现了国梁在西域的文学社交圈"
            ],
            "妻孥": [
                "• 亲情牵挂：家庭分离的无奈",
                "• 职责选择：只身赴任体现了对职务的重视",
                "• 时代特色：清代官员家庭与工作分离的常态",
                "• 情感支撑：虽分居但仍是重要的情感纽带"
            ],
            "西域各族民众": [
                "• 治理对象：地方官的职责所在",
                "• 观察视角：诗歌中生动描写各族风俗",
                "• 文化交融：华言、阿什、央哥等词汇体现了文化交流",
                "• 社会环境：构成了国梁任职期间的社会背景"
            ]
        }
    }
    
    return analysis

def main():
    """主函数"""
    print("正在创建国梁人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_guoliang_network()
    
    # 绘制图形
    fig = draw_guoliang_graph(G, characters)
    
    # 保存图片
    output_file = '国梁人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 国梁网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
        print(f"  证据: {data['evidence']}")
        print(f"  描述: {data['description']}")
        print()
    
    # 输出详细分析
    analysis = add_network_analysis()
    print("📋 网络关系深度分析:")
    for category, content in analysis.items():
        print(f"\n{category}:")
        if isinstance(content, list):
            for item in content:
                print(f"  {item}")
        elif isinstance(content, dict):
            for person, details in content.items():
                print(f"  {person}:")
                for detail in details:
                    print(f"    {detail}")
    
    print("\n" + "=" * 80)
    print("📖 总结:")
    print("   国梁的社交网络体现了清代地方官员的典型特征：")
    print("   既有严格的官场等级关系，又有温雅的文人交往；")
    print("   既承担着治理责任，又保持着家庭情感纽带。")
    print("   他的诗歌不仅是文学创作，更是其社会关系和生活状态的真实写照。")
    print("=" * 80)

if __name__ == "__main__":
    main()
