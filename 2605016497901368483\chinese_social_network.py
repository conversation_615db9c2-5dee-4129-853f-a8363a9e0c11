import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import matplotlib.font_manager as fm
import os

# 解决中文字体问题
def setup_chinese_font():
    """设置中文字体"""
    # Windows系统常见中文字体路径
    font_paths = [
        'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
        'C:/Windows/Fonts/simhei.ttf',    # 黑体
        'C:/Windows/Fonts/simsun.ttc',    # 宋体
        'C:/Windows/Fonts/simkai.ttf',    # 楷体
        'C:/Windows/Fonts/simfang.ttf',   # 仿宋
    ]
    
    # 尝试找到可用的中文字体
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.family'] = prop.get_name()
                plt.rcParams['axes.unicode_minus'] = False
                print(f"成功设置中文字体: {prop.get_name()}")
                return True
            except:
                continue
    
    # 如果找不到字体文件，使用系统字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
    plt.rcParams['axes.unicode_minus'] = False
    print("使用系统默认中文字体")
    return False

def create_chinese_network():
    """创建中文社交网络图"""
    
    G = nx.Graph()
    
    # 人物信息 - 纯中文
    characters = {
        '颜检': {'type': 'core', 'position': (0, 0), 'size': 5000},
        '成林': {'type': 'close_friend', 'position': (-3, 2.5), 'size': 3500},
        '遇昌': {'type': 'close_friend', 'position': (3, 2.5), 'size': 3500},
        '贡楚克札布': {'type': 'close_friend', 'position': (-3, -2.5), 'size': 3500},
        '李銮宣': {'type': 'close_friend', 'position': (3, -2.5), 'size': 3500},
        '晋昌': {'type': 'colleague', 'position': (-5.5, 0), 'size': 2800},
        '莫子捷': {'type': 'fellow_townsman', 'position': (5.5, 0), 'size': 2800}
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        ('颜检', '成林', {'weight': 4, 'relation': '密友', 'color': '#e74c3c'}),
        ('颜检', '遇昌', {'weight': 4, 'relation': '密友', 'color': '#e74c3c'}),
        ('颜检', '贡楚克札布', {'weight': 4, 'relation': '密友', 'color': '#e74c3c'}),
        ('颜检', '李銮宣', {'weight': 4, 'relation': '密友', 'color': '#e74c3c'}),
        ('颜检', '晋昌', {'weight': 2, 'relation': '诗友', 'color': '#3498db'}),
        ('颜检', '莫子捷', {'weight': 1, 'relation': '同乡', 'color': '#27ae60'}),
        ('成林', '遇昌', {'weight': 2, 'relation': '同游', 'color': '#95a5a6'}),
        ('遇昌', '贡楚克札布', {'weight': 2, 'relation': '同游', 'color': '#95a5a6'}),
        ('贡楚克札布', '李銮宣', {'weight': 2, 'relation': '同游', 'color': '#95a5a6'}),
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_chinese_graph(G, characters):
    """绘制中文网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    fig.patch.set_facecolor('#f5f5f5')
    ax.set_facecolor('#f5f5f5')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',           # 红色 - 核心人物
        'close_friend': '#3498db',   # 蓝色 - 密友
        'colleague': '#9b59b6',      # 紫色 - 同僚
        'fellow_townsman': '#27ae60' # 绿色 - 同乡
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2,
                              alpha=0.7, 
                              edge_color=color)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=3)
    
    # 添加标签
    labels = {}
    for name, info in characters.items():
        if name == '颜检':
            labels[name] = '颜检\n(岱云)'
        elif name == '成林':
            labels[name] = '成林\n(漪园)'
        elif name == '遇昌':
            labels[name] = '遇昌\n(晓亭)'
        elif name == '贡楚克札布':
            labels[name] = '贡楚克札布\n(果斋)'
        elif name == '李銮宣':
            labels[name] = '李銮宣\n(石农)'
        elif name == '莫子捷':
            labels[name] = '莫子捷\n(远崖)'
        else:
            labels[name] = name
    
    nx.draw_networkx_labels(G, pos, labels,
                           font_size=12, 
                           font_weight='bold',
                           font_color='white')
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物', alpha=0.9),
        mpatches.Patch(color=node_colors['close_friend'], label='一级核心圈（谪戍挚友）', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='二级关系圈（诗友同僚）', alpha=0.9),
        mpatches.Patch(color=node_colors['fellow_townsman'], label='同乡关系', alpha=0.9)
    ]
    
    legend = ax.legend(handles=legend_elements, 
                      loc='upper left', 
                      bbox_to_anchor=(0.02, 0.98),
                      frameon=True,
                      fancybox=True,
                      shadow=True,
                      fontsize=13,
                      title='人物关系类型',
                      title_fontsize=14)
    
    # 标题
    ax.text(0.5, 0.95, "颜检人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '嘉庆十一年至十三年 乌鲁木齐谪戍期间 (1807-1808)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-7, 7)
    ax.set_ylim(-4, 4)
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在设置中文字体...")
    setup_chinese_font()
    
    print("正在创建颜检人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_chinese_network()
    
    # 绘制图形
    fig = draw_chinese_graph(G, characters)
    
    # 保存图片
    output_file = '颜检人物社会关系网络图_中文版.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"中文版网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
    
    print("\n核心活动:")
    print("• 诗文唱和、同游名胜、宴集聚会、相互慰藉")
    print("• 主要地点: 乌鲁木齐、红山、大石头山寺、迪化城")
    print("• 时间背景: 嘉庆十一年至十三年（1807-1808年）")

if __name__ == "__main__":
    main()
