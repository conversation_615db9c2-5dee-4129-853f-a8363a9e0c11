import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np
from matplotlib.patches import FancyBboxPatch
import matplotlib.patheffects as path_effects

# 设置样式
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
plt.rcParams['font.size'] = 10

def create_modern_network():
    """创建现代风格的颜检社交网络图"""
    
    G = nx.Graph()
    
    # 人物信息 - 简化中文名称
    characters = {
        'YanJian': {'type': 'core', 'position': (0, 0), 'label': '<PERSON>\n颜检', 'size': 6000},
        'ChengLin': {'type': 'close_friend', 'position': (-3, 2.5), 'label': 'Cheng <PERSON>\n成林', 'size': 4000},
        'YuChang': {'type': 'close_friend', 'position': (3, 2.5), 'label': '<PERSON>\n遇昌', 'size': 4000},
        '<PERSON><PERSON><PERSON>': {'type': 'close_friend', 'position': (-3, -2.5), 'label': '<PERSON>\n果斋', 'size': 4000},
        '<PERSON><PERSON>ong': {'type': 'close_friend', 'position': (3, -2.5), 'label': 'Shi Nong\n石农', 'size': 4000},
        '<PERSON>Chang': {'type': 'colleague', 'position': (-5.5, 0), 'label': 'Jin Chang\n晋昌', 'size': 3000},
        'YuanYa': {'type': 'fellow_townsman', 'position': (5.5, 0), 'label': 'Yuan Ya\n远崖', 'size': 3000}
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        ('YanJian', 'ChengLin', {'weight': 4, 'relation': 'Close Friend', 'color': '#FF6B6B'}),
        ('YanJian', 'YuChang', {'weight': 4, 'relation': 'Close Friend', 'color': '#FF6B6B'}),
        ('YanJian', 'GuoZhai', {'weight': 4, 'relation': 'Close Friend', 'color': '#FF6B6B'}),
        ('YanJian', 'ShiNong', {'weight': 4, 'relation': 'Close Friend', 'color': '#FF6B6B'}),
        ('YanJian', 'JinChang', {'weight': 2, 'relation': 'Poetry Friend', 'color': '#4ECDC4'}),
        ('YanJian', 'YuanYa', {'weight': 1, 'relation': 'Fellow Townsman', 'color': '#45B7D1'}),
        ('ChengLin', 'YuChang', {'weight': 2, 'relation': 'Companion', 'color': '#95A5A6'}),
        ('YuChang', 'GuoZhai', {'weight': 2, 'relation': 'Companion', 'color': '#95A5A6'}),
        ('GuoZhai', 'ShiNong', {'weight': 2, 'relation': 'Companion', 'color': '#95A5A6'}),
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_modern_graph(G, characters):
    """绘制现代风格的网络图"""
    
    # 创建图形 - 使用深色主题
    fig, ax = plt.subplots(1, 1, figsize=(18, 14))
    fig.patch.set_facecolor('#1e1e1e')
    ax.set_facecolor('#1e1e1e')
    
    # 现代配色方案
    node_colors = {
        'core': '#FF6B6B',           # 亮红色 - 核心人物
        'close_friend': '#4ECDC4',   # 青绿色 - 密友
        'colleague': '#45B7D1',      # 蓝色 - 同僚
        'fellow_townsman': '#96CEB4' # 薄荷绿 - 同乡
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系类型使用不同颜色
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        
        # 绘制边的光晕效果
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 3,
                              alpha=0.2, 
                              edge_color=color)
        
        # 绘制主边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 1.5,
                              alpha=0.8, 
                              edge_color=color)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        position = node_data['position']
        
        # 绘制节点光晕
        circle_glow = plt.Circle(position, 0.4, 
                               color=color, alpha=0.3, 
                               transform=ax.transData)
        ax.add_patch(circle_glow)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签 - 使用白色文字
    labels = {name: info['label'] for name, info in characters.items()}
    nx.draw_networkx_labels(G, pos, labels,
                           font_size=11, 
                           font_weight='bold',
                           font_color='white')
    
    # 创建现代风格图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='Core Figure (核心人物)', alpha=0.9),
        mpatches.Patch(color=node_colors['close_friend'], label='Inner Circle (核心圈)', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='Poetry Friends (诗友)', alpha=0.9),
        mpatches.Patch(color=node_colors['fellow_townsman'], label='Fellow Townsman (同乡)', alpha=0.9)
    ]
    
    legend = ax.legend(handles=legend_elements, 
                      loc='upper left', 
                      bbox_to_anchor=(0.02, 0.98),
                      frameon=True,
                      fancybox=True,
                      shadow=True,
                      fontsize=12,
                      facecolor='#2c2c2c',
                      edgecolor='white',
                      labelcolor='white')
    
    # 现代风格标题
    title = ax.text(0.5, 0.95, "Yan Jian's Social Network", 
                   transform=ax.transAxes,
                   fontsize=26, 
                   fontweight='bold',
                   ha='center',
                   color='white')
    
    subtitle = ax.text(0.5, 0.91, 'Urumqi Exile Period • 乌鲁木齐谪戍期间 (1807-1808)', 
                      transform=ax.transAxes,
                      fontsize=16, 
                      ha='center',
                      color='#cccccc',
                      style='italic')
    
    # 添加发光效果
    title.set_path_effects([path_effects.withStroke(linewidth=3, foreground='#FF6B6B', alpha=0.5)])
    
    # 添加网络统计信息
    stats_text = f"Nodes: {G.number_of_nodes()} | Edges: {G.number_of_edges()} | Density: {nx.density(G):.3f}"
    ax.text(0.5, 0.05, stats_text,
           transform=ax.transAxes,
           fontsize=12,
           ha='center',
           color='#888888')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-7, 7)
    ax.set_ylim(-4, 4)
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("Creating modern social network visualization...")
    
    # 创建网络
    G, characters = create_modern_network()
    
    # 绘制图形
    fig = draw_modern_graph(G, characters)
    
    # 保存图片
    output_file = 'Modern_Yan_Jian_Social_Network.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='#1e1e1e', edgecolor='none')
    
    print(f"Modern network graph saved as: {output_file}")
    
    # 输出关系信息
    print("\nRelationship Details:")
    for edge in G.edges(data=True):
        source, target, data = edge
        source_label = characters[source]['label'].replace('\n', ' ')
        target_label = characters[target]['label'].replace('\n', ' ')
        print(f"• {source_label} ↔ {target_label}: {data['relation']}")

if __name__ == "__main__":
    main()
