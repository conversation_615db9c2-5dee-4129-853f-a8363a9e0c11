import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_qiyunshi_network():
    """创建祁韵士的人物社会关系网络图"""
    
    G = nx.Graph()
    
    # 人物信息
    characters = {
        '祁韵士': {
            'type': 'core', 
            'position': (0, 0), 
            'size': 5000, 
            'label': '祁韵士\n(谐庭/鹤皋)',
            'description': '山西寿阳人，谪戍官员转幕僚学者'
        },
        '松筠': {
            'type': 'patron', 
            'position': (-3, 2), 
            'size': 4000, 
            'label': '松筠\n伊犁将军→两江总督',
            'description': '祁韵士最重要的赏识者和庇护人'
        },
        '那彦成': {
            'type': 'employer', 
            'position': (3, 2), 
            'size': 3500, 
            'label': '那彦成\n陕甘总督→直隶总督',
            'description': '聘请祁韵士为家庭教师的封疆大吏'
        }
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义 - 包含详细的时间线和关系变化
    relationships = [
        ('祁韵士', '松筠', {
            'weight': 5, 
            'relation': '上级→赏识者→幕僚关系',
            'timeline': [
                '1804-1808年：伊犁将军时期，任命为印房章京',
                '1810年：两江总督时期，聘为幕僚'
            ],
            'significance': '人生转折的关键人物',
            'color': '#e74c3c'
        }),
        ('祁韵士', '那彦成', {
            'weight': 3, 
            'relation': '雇主→家庭教师关系',
            'timeline': [
                '1811年：兰州时期，聘往课其子',
                '1814年：直隶时期，随任至署中课读'
            ],
            'significance': '晚年重要的庇护者',
            'color': '#3498db'
        })
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_qiyunshi_graph(G, characters):
    """绘制祁韵士网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',      # 红色 - 核心人物
        'patron': '#3498db',    # 蓝色 - 赏识者/庇护人
        'employer': '#27ae60'   # 绿色 - 雇主
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系重要性使用不同样式
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        
        # 绘制边的光晕效果
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2,
                              alpha=0.3, 
                              edge_color=color)
        
        # 绘制主边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 1.2,
                              alpha=0.8, 
                              edge_color=color)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点光晕
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size + 300,
                             alpha=0.3)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小
        if data['type'] == 'core':
            fontsize = 14
            bbox_color = '#2c3e50'
        else:
            fontsize = 11
            bbox_color = '#34495e'
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.4", 
                            facecolor=bbox_color, 
                            alpha=0.7,
                            edgecolor='none'))
    
    # 删除关系说明文本框
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物（祁韵士）', alpha=0.9),
        mpatches.Patch(color=node_colors['patron'], label='赏识者/庇护人（松筠）', alpha=0.9),
        mpatches.Patch(color=node_colors['employer'], label='雇主/教育对象（那彦成）', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=12,
             title='人物关系类型',
             title_fontsize=13)
    
    # 标题
    ax.text(0.5, 0.95, "祁韵士人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '嘉庆九年至十九年 谪戍伊犁及赦免后幕僚生涯 (1804-1814)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 删除祁韵士的基本信息文本框
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-5, 5)
    ax.set_ylim(-3, 3)
    
    plt.tight_layout()
    return fig

def add_detailed_analysis():
    """添加详细的关系分析"""
    
    analysis = {
        "网络特点": [
            "• 核心型网络：祁韵士为绝对中心，其他人物围绕其展开",
            "• 垂直关系：主要体现上下级、庇护与被庇护的关系",
            "• 时间跨度：覆盖谪戍期间及赦免后的关键十年（1804-1814）",
            "• 地域流动：从伊犁到江南，再到兰州、直隶，体现了人物的地理流动"
        ],
        
        "关系深度分析": {
            "松筠": [
                "• 关系演变：上级→赏识者→幕僚雇主",
                "• 关键作用：人生转折的关键人物，给予了从戴罪之身到重要幕僚的机会",
                "• 信任程度：极高，从谪戍期间的重用到赦免后的立即聘用",
                "• 影响范围：职业生涯的重新起步和社会地位的恢复"
            ],
            "那彦成": [
                "• 关系性质：基于学识认可的雇佣关系",
                "• 信任表现：聘为子弟师傅，并随任调动",
                "• 社会意义：体现了祁韵士学者身份的社会认可",
                "• 稳定性：长期的雇佣关系，提供了生活保障"
            ]
        },
        
        "历史意义": [
            "• 体现了清代官员谪戍制度中的人情温暖",
            "• 展现了封疆大吏对人才的识别和保护",
            "• 反映了清代幕僚制度和家庭教育的特点",
            "• 说明了个人才能在逆境中的重要作用"
        ]
    }
    
    return analysis

def main():
    """主函数"""
    print("正在创建祁韵士人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_qiyunshi_network()
    
    # 绘制图形
    fig = draw_qiyunshi_graph(G, characters)
    
    # 保存图片
    output_file = '祁韵士人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 祁韵士网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
        for timeline in data['timeline']:
            print(f"  - {timeline}")
        print(f"  意义: {data['significance']}")
        print()
    
    # 输出详细分析
    analysis = add_detailed_analysis()
    print("📋 网络关系深度分析:")
    for category, content in analysis.items():
        print(f"\n{category}:")
        if isinstance(content, list):
            for item in content:
                print(f"  {item}")
        elif isinstance(content, dict):
            for person, details in content.items():
                print(f"  {person}:")
                for detail in details:
                    print(f"    {detail}")
    
    print("\n" + "=" * 80)
    print("📖 总结:")
    print("   祁韵士的社交网络虽然规模不大，但关系质量极高。")
    print("   松筠和那彦成两位封疆大吏的赏识和庇护，")
    print("   使得祁韵士从一个谪戍罪人转变为受人尊敬的学者幕僚。")
    print("   这个网络体现了清代官场中'伯乐识千里马'的人才发现机制，")
    print("   以及基于个人才能和品德的信任关系。")
    print("=" * 80)

if __name__ == "__main__":
    main()
