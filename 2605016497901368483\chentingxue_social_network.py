import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_chentingxue_network():
    """创建陈庭学的人物社会关系网络图"""
    
    G = nx.Graph()
    
    # 人物信息
    characters = {
        '陈庭学': {
            'type': 'core', 
            'position': (0, 0), 
            'size': 5500, 
            'label': '陈庭学\n(莼葑)',
            'description': '谪戍文人，伊犁文人圈核心'
        },
        # 核心文人圈（谪戍友人）
        '庄肇奎': {
            'type': 'core_friend', 
            'position': (-2.5, 2.5), 
            'size': 4000, 
            'label': '庄肇奎\n(胥园)',
            'description': '最核心的诗友，频繁唱和'
        },
        '于梅谷': {
            'type': 'core_friend', 
            'position': (2.5, 2.5), 
            'size': 3500, 
            'label': '于梅谷\n(行五)',
            'description': '雅集主人，寄亭主人'
        },
        '冯蓼堂': {
            'type': 'core_friend', 
            'position': (-3, 0), 
            'size': 3000, 
            'label': '冯蓼堂',
            'description': '文人圈成员，先期获释'
        },
        '朱端书': {
            'type': 'core_friend', 
            'position': (3, 0), 
            'size': 3000, 
            'label': '朱端书',
            'description': '诗友，风筝诗唱和'
        },
        '徐溉余': {
            'type': 'core_friend', 
            'position': (-2, -2.5), 
            'size': 2800, 
            'label': '徐溉余',
            'description': '文人圈成员，后官复原职'
        },
        '夏蘧庄': {
            'type': 'core_friend', 
            'position': (2, -2.5), 
            'size': 2800, 
            'label': '夏蘧庄',
            'description': '文人圈成员，后官复原职'
        },
        # 上层军政官员
        '奎林': {
            'type': 'superior', 
            'position': (0, 4), 
            'size': 3800, 
            'label': '奎林\n(奎元戎)',
            'description': '伊犁将军，题壁诗唱和'
        },
        '刘鉴': {
            'type': 'superior', 
            'position': (-4.5, 1.5), 
            'size': 3200, 
            'label': '刘鉴\n(刘军门)',
            'description': '军门，九日登高唱和'
        },
        # 其他交往人物
        '庆八静斋': {
            'type': 'colleague', 
            'position': (4.5, 1.5), 
            'size': 2500, 
            'label': '庆八静斋',
            'description': '公务同事，出差旅伴'
        },
        '杨大': {
            'type': 'colleague', 
            'position': (-4, -1.5), 
            'size': 2300, 
            'label': '杨大\n(司储)',
            'description': '仓储官员，古尔札相遇'
        },
        '申瑶泉': {
            'type': 'poet_friend', 
            'position': (4, -1.5), 
            'size': 2500, 
            'label': '申瑶泉',
            'description': '诗友，麦浪诗次韵'
        },
        '龙铎': {
            'type': 'friend', 
            'position': (0, -4), 
            'size': 2600, 
            'label': '龙铎\n(木天)',
            'description': '友人，乌鲁木齐感旧'
        },
        '连彭年': {
            'type': 'fellow_townsman', 
            'position': (-1.5, -3.5), 
            'size': 2400, 
            'label': '连彭年\n(连司马)',
            'description': '同乡，济木萨相遇'
        }
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        # 核心文人圈 - 强连接
        ('陈庭学', '庄肇奎', {
            'weight': 5, 
            'relation': '最核心诗友',
            'evidence': '《蓼堂去后，胥园来诗》等大量唱和',
            'description': '频繁诗歌唱和，关系最密切',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('陈庭学', '于梅谷', {
            'weight': 4, 
            'relation': '雅集主人',
            'evidence': '《赠于梅谷二首》，寄亭聚会',
            'description': '文人圈雅集场所提供者',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('陈庭学', '冯蓼堂', {
            'weight': 4, 
            'relation': '文人圈友人',
            'evidence': '《蓼堂去后，胥园来诗》',
            'description': '共同雅集，先期获释',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('陈庭学', '朱端书', {
            'weight': 3, 
            'relation': '诗歌唱和',
            'evidence': '《次韵朱端书风鸢诗二首》',
            'description': '以风筝为题的诗歌唱和',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('陈庭学', '徐溉余', {
            'weight': 3, 
            'relation': '文人圈友人',
            'evidence': '庄肇奎诗注提及共同聚会',
            'description': '寄亭聚会成员，后官复原职',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('陈庭学', '夏蘧庄', {
            'weight': 3, 
            'relation': '文人圈友人',
            'evidence': '庄肇奎诗注提及共同聚会',
            'description': '寄亭聚会成员，后官复原职',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        # 上层军政官员 - 中等连接
        ('陈庭学', '奎林', {
            'weight': 2, 
            'relation': '下级对上级唱和',
            'evidence': '《奉和奎元戎鉴远楼题壁韵二首》',
            'description': '对伊犁将军的敬意唱和',
            'color': '#3498db',
            'style': 'solid'
        }),
        ('陈庭学', '刘鉴', {
            'weight': 2, 
            'relation': '社交往来',
            'evidence': '《和刘军门九日登高二首》',
            'description': '共同登高过节，诗歌唱和',
            'color': '#3498db',
            'style': 'solid'
        }),
        # 其他交往 - 弱连接
        ('陈庭学', '庆八静斋', {
            'weight': 1, 
            'relation': '公务同事',
            'evidence': '《春日偕庆八静斋于役古尔札》',
            'description': '公务出差的旅伴',
            'color': '#f39c12',
            'style': 'dashed'
        }),
        ('陈庭学', '杨大', {
            'weight': 1, 
            'relation': '公务相遇',
            'evidence': '《晤司储杨大》',
            'description': '古尔札与仓储官员相遇',
            'color': '#f39c12',
            'style': 'dashed'
        }),
        ('陈庭学', '申瑶泉', {
            'weight': 2, 
            'relation': '诗歌交流',
            'evidence': '《读申瑶泉麦浪诗次韵》',
            'description': '诗歌次韵交流',
            'color': '#27ae60',
            'style': 'solid'
        }),
        ('陈庭学', '龙铎', {
            'weight': 2, 
            'relation': '友人',
            'evidence': '《乌鲁木齐感旧兼赠龙四兄木天》',
            'description': '称兄道弟的友人关系',
            'color': '#9b59b6',
            'style': 'solid'
        }),
        ('陈庭学', '连彭年', {
            'weight': 1, 
            'relation': '同乡',
            'evidence': '《济木萨晤连司马》',
            'description': '同乡关系，偶然相遇',
            'color': '#95a5a6',
            'style': 'dashed'
        })
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_chentingxue_graph(G, characters):
    """绘制陈庭学网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(18, 16))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',              # 红色 - 核心人物
        'core_friend': '#3498db',       # 蓝色 - 核心文人圈
        'superior': '#9b59b6',          # 紫色 - 上层官员
        'colleague': '#f39c12',         # 橙色 - 公务同事
        'poet_friend': '#27ae60',       # 绿色 - 诗友
        'friend': '#e67e22',            # 深橙色 - 友人
        'fellow_townsman': '#95a5a6'    # 灰色 - 同乡
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系类型使用不同样式
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        style = data['style']
        
        # 绘制边的光晕效果
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2.5,
                              alpha=0.3, 
                              edge_color=color,
                              style=style)
        
        # 绘制主边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 1.5,
                              alpha=0.8, 
                              edge_color=color,
                              style=style)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点光晕
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size + 400,
                             alpha=0.3)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小和颜色
        if data['type'] == 'core':
            fontsize = 14
            bbox_color = '#2c3e50'
        elif data['type'] == 'core_friend':
            fontsize = 11
            bbox_color = '#34495e'
        elif data['type'] == 'superior':
            fontsize = 11
            bbox_color = '#34495e'
        else:
            fontsize = 10
            bbox_color = '#34495e'
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.4", 
                            facecolor=bbox_color, 
                            alpha=0.7,
                            edgecolor='none'))
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物（陈庭学）', alpha=0.9),
        mpatches.Patch(color=node_colors['core_friend'], label='核心文人圈（谪戍友人）', alpha=0.9),
        mpatches.Patch(color=node_colors['superior'], label='上层军政官员', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='公务同事', alpha=0.9),
        mpatches.Patch(color=node_colors['poet_friend'], label='诗歌交流', alpha=0.9),
        mpatches.Patch(color=node_colors['friend'], label='友人', alpha=0.9),
        mpatches.Patch(color=node_colors['fellow_townsman'], label='同乡', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=10,
             title='人物关系类型',
             title_fontsize=11)
    
    # 标题
    ax.text(0.5, 0.95, "陈庭学人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '伊犁谪戍期间文人社交网络', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-6, 6)
    ax.set_ylim(-5, 5)
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在创建陈庭学人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_chentingxue_network()
    
    # 绘制图形
    fig = draw_chentingxue_graph(G, characters)
    
    # 保存图片
    output_file = '陈庭学人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 陈庭学网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
        print(f"  证据: {data['evidence']}")
        print(f"  描述: {data['description']}")
        print()
    
    print("\n" + "=" * 80)
    print("📖 总结:")
    print("   陈庭学的社交网络展现了清代谪戍文人的典型生活状态：")
    print("   以诗歌为纽带，在逆境中建立了活跃的文人社交圈；")
    print("   既有深厚的友谊，又维持着与上层官员的必要联系。")
    print("   这个网络体现了文人在边疆的精神慰藉和文化传承。")
    print("=" * 80)

if __name__ == "__main__":
    main()
