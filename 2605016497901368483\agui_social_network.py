import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_agui_network():
    """创建阿桂的人物社会关系网络图"""
    
    # 创建有向图，因为有明确的上下级关系
    G = nx.DiGraph()
    
    # 人物信息
    characters = {
        '阿桂': {
            'type': 'core', 
            'position': (0, 0), 
            'size': 5500, 
            'label': '阿桂\n(1717-1797)',
            'description': '清代著名将领，西域平叛功臣'
        },
        # 家族关系
        '阿克敦': {
            'type': 'family', 
            'position': (0, 3), 
            'size': 3500, 
            'label': '阿克敦\n(父亲)',
            'description': '阿桂的父亲'
        },
        # 上级将领
        '傅恒': {
            'type': 'superior', 
            'position': (-3, 2), 
            'size': 4500, 
            'label': '傅恒\n协办大学士/经略',
            'description': '阿桂的上级，两次征战统帅'
        },
        '富德': {
            'type': 'superior', 
            'position': (3, 2), 
            'size': 4000, 
            'label': '富德\n定边左副将军',
            'description': '平定大小和卓时的上级'
        },
        # 同僚
        '明瑞': {
            'type': 'colleague', 
            'position': (-3, -1), 
            'size': 4000, 
            'label': '明瑞\n伊犁将军',
            'description': '协作平叛的同僚，前后任关系'
        },
        # 下属
        '奎林': {
            'type': 'subordinate', 
            'position': (3, -2), 
            'size': 3500, 
            'label': '奎林\n领队大臣',
            'description': '征金川时的下属将领'
        }
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义 - 使用有向边表示等级关系
    relationships = [
        # 家族关系 - 无向
        ('阿克敦', '阿桂', {
            'weight': 5, 
            'relation': '父子关系',
            'evidence': '阿桂作者介绍："阿克敦子"',
            'description': '直系血缘关系',
            'color': '#9b59b6',
            'style': 'solid',
            'directed': False
        }),
        # 上级关系 - 有向（从上级指向下级）
        ('傅恒', '阿桂', {
            'weight': 4, 
            'relation': '上级→下属',
            'evidence': '征大金川(1748)、征缅甸(1768)',
            'description': '两次征战的统帅与下属关系',
            'color': '#e74c3c',
            'style': 'solid',
            'directed': True
        }),
        ('富德', '阿桂', {
            'weight': 3, 
            'relation': '上级→下属',
            'evidence': '平定大小和卓叛乱(1759)',
            'description': '追击叛军时的指挥关系',
            'color': '#e74c3c',
            'style': 'solid',
            'directed': True
        }),
        # 同僚关系 - 无向
        ('阿桂', '明瑞', {
            'weight': 3, 
            'relation': '同僚/协作',
            'evidence': '协助平定乌什起义(1765)，巴燕岱城前后任',
            'description': '军事协作与前后任关系',
            'color': '#3498db',
            'style': 'solid',
            'directed': False
        }),
        # 下属关系 - 有向（从上级指向下级）
        ('阿桂', '奎林', {
            'weight': 3, 
            'relation': '上级→下属',
            'evidence': '征金川(1772)，奎林为领队大臣',
            'description': '征金川时的指挥关系',
            'color': '#27ae60',
            'style': 'solid',
            'directed': True
        })
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        if attrs['directed']:
            G.add_edge(source, target, **attrs)
        else:
            # 对于无向关系，添加到无向图中处理
            G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_agui_graph(G, characters):
    """绘制阿桂网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',          # 红色 - 核心人物
        'family': '#9b59b6',        # 紫色 - 家族
        'superior': '#3498db',      # 蓝色 - 上级
        'colleague': '#f39c12',     # 橙色 - 同僚
        'subordinate': '#27ae60'    # 绿色 - 下属
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系类型使用不同样式
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        style = data['style']
        directed = data['directed']
        
        # 绘制边的光晕效果
        if directed:
            # 有向边
            nx.draw_networkx_edges(G, pos, 
                                  edgelist=[(source, target)],
                                  width=weight * 2,
                                  alpha=0.3, 
                                  edge_color=color,
                                  style=style,
                                  arrows=True,
                                  arrowsize=25,
                                  arrowstyle='->')
        
        # 绘制主边
        if directed:
            nx.draw_networkx_edges(G, pos, 
                                  edgelist=[(source, target)],
                                  width=weight * 1.2,
                                  alpha=0.8, 
                                  edge_color=color,
                                  style=style,
                                  arrows=True,
                                  arrowsize=20,
                                  arrowstyle='->')
        else:
            # 无向边（家族和同僚关系）
            nx.draw_networkx_edges(G, pos, 
                                  edgelist=[(source, target)],
                                  width=weight * 1.2,
                                  alpha=0.8, 
                                  edge_color=color,
                                  style=style,
                                  arrows=False)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点光晕
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size + 400,
                             alpha=0.3)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小和颜色
        if data['type'] == 'core':
            fontsize = 14
            bbox_color = '#2c3e50'
        elif data['type'] in ['superior', 'family']:
            fontsize = 11
            bbox_color = '#34495e'
        else:
            fontsize = 10
            bbox_color = '#34495e'
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.4", 
                            facecolor=bbox_color, 
                            alpha=0.7,
                            edgecolor='none'))
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物（阿桂）', alpha=0.9),
        mpatches.Patch(color=node_colors['family'], label='家族成员（阿克敦）', alpha=0.9),
        mpatches.Patch(color=node_colors['superior'], label='上级将领（傅恒/富德）', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='同僚（明瑞）', alpha=0.9),
        mpatches.Patch(color=node_colors['subordinate'], label='下属（奎林）', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=11,
             title='人物关系类型',
             title_fontsize=12)
    
    # 添加关系说明
    relationship_text = """
主要军事行动：
• 1748年：征大金川（随傅恒）
• 1759年：平大小和卓（从富德）
• 1765年：平乌什起义（助明瑞）
• 1768年：征缅甸（偕傅恒）
• 1772年：征金川（统奎林）
    """
    
    ax.text(0.98, 0.15, relationship_text,
           transform=ax.transAxes,
           fontsize=10,
           ha='right',
           va='bottom',
           bbox=dict(boxstyle="round,pad=0.5", 
                    facecolor='white', 
                    alpha=0.9,
                    edgecolor='gray'))
    
    # 标题
    ax.text(0.5, 0.95, "阿桂人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '清代著名将领军政关系网络 (1717-1797)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-5, 5)
    ax.set_ylim(-3, 4)
    
    plt.tight_layout()
    return fig

def add_network_analysis():
    """添加网络分析"""
    
    analysis = {
        "网络特点": [
            "• 等级分明：清晰的上下级军事指挥关系",
            "• 家族传承：父子两代的军事世家",
            "• 多战区经验：大金川、西域、缅甸等多个战场",
            "• 指挥能力：从下属成长为统帅的完整军事生涯",
            "• 协作关系：与同级将领的军事协作"
        ],
        
        "关系分析": {
            "家族关系": [
                "• 阿克敦：父亲，军事世家的传承",
                "• 体现了清代满族军事贵族的家族特色"
            ],
            "上级关系": [
                "• 傅恒：两次重要征战的统帅，对阿桂军事生涯影响重大",
                "• 富德：西域平叛时期的直接上级",
                "• 体现了清代军事指挥体系的等级秩序"
            ],
            "同僚关系": [
                "• 明瑞：军事协作伙伴，前后任关系",
                "• 体现了高级将领之间的协调配合"
            ],
            "下属关系": [
                "• 奎林：征金川时的下属，体现了阿桂的指挥能力",
                "• 反映了阿桂从被指挥者到指挥者的成长"
            ]
        },
        
        "历史意义": [
            "• 展现了清代军事指挥体系的运作模式",
            "• 体现了满族军事贵族的成长轨迹",
            "• 反映了清代多民族统一战争的组织形式",
            "• 说明了个人能力在军事等级制度中的重要作用"
        ]
    }
    
    return analysis

def main():
    """主函数"""
    print("正在创建阿桂人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_agui_network()
    
    # 绘制图形
    fig = draw_agui_graph(G, characters)
    
    # 保存图片
    output_file = '阿桂人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 阿桂网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        direction = "→" if data['directed'] else "↔"
        print(f"• {source} {direction} {target}: {data['relation']}")
        print(f"  证据: {data['evidence']}")
        print(f"  描述: {data['description']}")
        print()
    
    # 输出详细分析
    analysis = add_network_analysis()
    print("📋 网络关系深度分析:")
    for category, content in analysis.items():
        print(f"\n{category}:")
        if isinstance(content, list):
            for item in content:
                print(f"  {item}")
        elif isinstance(content, dict):
            for relation_type, details in content.items():
                print(f"  {relation_type}:")
                for detail in details:
                    print(f"    {detail}")
    
    print("\n" + "=" * 80)
    print("📖 总结:")
    print("   阿桂的社交网络体现了清代军事指挥体系的典型特征：")
    print("   等级分明的上下级关系，家族传承的军事传统，")
    print("   以及从下属成长为统帅的完整军事生涯轨迹。")
    print("   这个网络展现了清代多民族统一战争中的组织形式和人才培养机制。")
    print("=" * 80)

if __name__ == "__main__":
    main()
