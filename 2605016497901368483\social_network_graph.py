import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
from matplotlib import font_manager
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_social_network():
    """创建颜检的人物社会关系网络图"""
    
    # 创建有向图
    G = nx.Graph()
    
    # 定义人物信息
    characters = {
        # 核心人物
        '颜检\n(岱云)': {'type': 'core', 'position': (0, 0)},
        
        # 一级核心圈 - 乌鲁木齐谪戍挚友
        '成林\n(漪园)': {'type': 'close_friend', 'position': (-2, 2)},
        '遇昌\n(晓亭)': {'type': 'close_friend', 'position': (2, 2)},
        '贡楚克札布\n(果斋)': {'type': 'close_friend', 'position': (-2, -2)},
        '李銮宣\n(石农)': {'type': 'close_friend', 'position': (2, -2)},
        
        # 二级关系圈
        '晋昌': {'type': 'colleague', 'position': (-4, 0)},
        '莫子捷\n(远崖)': {'type': 'fellow_townsman', 'position': (4, 0)}
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 定义关系和边
    relationships = [
        # 颜检与一级核心圈的关系
        ('颜检\n(岱云)', '成林\n(漪园)', {'relation': '密友', 'activity': '同游红山、共赴迪化城', 'weight': 3}),
        ('颜检\n(岱云)', '遇昌\n(晓亭)', {'relation': '密友', 'activity': '同游、诗文唱和', 'weight': 3}),
        ('颜检\n(岱云)', '贡楚克札布\n(果斋)', {'relation': '密友', 'activity': '同游大石头山寺', 'weight': 3}),
        ('颜检\n(岱云)', '李銮宣\n(石农)', {'relation': '密友', 'activity': '诗文往来、住处相近', 'weight': 3}),
        
        # 颜检与二级关系圈的关系
        ('颜检\n(岱云)', '晋昌', {'relation': '诗友', 'activity': '和诗唱和', 'weight': 2}),
        ('颜检\n(岱云)', '莫子捷\n(远崖)', {'relation': '同乡', 'activity': '济木萨相遇', 'weight': 1}),
        
        # 核心圈内部的关系
        ('成林\n(漪园)', '遇昌\n(晓亭)', {'relation': '同游伙伴', 'activity': '共同出游', 'weight': 2}),
        ('遇昌\n(晓亭)', '贡楚克札布\n(果斋)', {'relation': '同游伙伴', 'activity': '共同出游', 'weight': 2}),
        ('贡楚克札布\n(果斋)', '李銮宣\n(石农)', {'relation': '谪戍同僚', 'activity': '共同生活', 'weight': 2}),
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_network_graph(G, characters):
    """绘制网络关系图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # 定义颜色方案
    colors = {
        'core': '#FF6B6B',           # 红色 - 核心人物
        'close_friend': '#4ECDC4',   # 青色 - 密友
        'colleague': '#45B7D1',      # 蓝色 - 同僚
        'fellow_townsman': '#96CEB4' # 绿色 - 同乡
    }
    
    # 定义节点大小
    sizes = {
        'core': 3000,
        'close_friend': 2000,
        'colleague': 1500,
        'fellow_townsman': 1500
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边
    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
    nx.draw_networkx_edges(G, pos, 
                          width=[w * 1.5 for w in edge_weights],
                          alpha=0.6, 
                          edge_color='gray')
    
    # 按类型绘制节点
    for node_type, color in colors.items():
        nodes = [name for name, info in characters.items() if info['type'] == node_type]
        if nodes:
            nx.draw_networkx_nodes(G, pos, 
                                 nodelist=nodes,
                                 node_color=color,
                                 node_size=[sizes[node_type] for _ in nodes],
                                 alpha=0.8)
    
    # 添加标签
    nx.draw_networkx_labels(G, pos, 
                           font_size=10, 
                           font_weight='bold',
                           font_color='black')
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=colors['core'], label='核心人物 (颜检)'),
        mpatches.Patch(color=colors['close_friend'], label='一级核心圈 (谪戍挚友)'),
        mpatches.Patch(color=colors['colleague'], label='二级关系圈 (诗友同僚)'),
        mpatches.Patch(color=colors['fellow_townsman'], label='同乡关系')
    ]
    
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 0.98))
    
    # 设置标题和样式
    plt.title('颜检人物社会关系网络图\n(嘉庆十一年-十三年 乌鲁木齐谪戍期间)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 调整布局
    plt.tight_layout()
    
    return fig

def add_relationship_annotations(G, characters):
    """添加关系说明文本"""
    
    # 创建关系说明
    relationship_text = """
主要关系说明：

一级核心圈（谪戍挚友）：
• 成林（漪园）- 前广西巡抚，同游红山、共赴迪化城
• 遇昌（晓亭）- 前江苏按察使，同游、诗文唱和
• 贡楚克札布（果斋）- 前察哈尔都统，同游大石头山寺
• 李銮宣（石农）- 前云南按察使，诗文往来、住处相近

二级关系圈：
• 晋昌 - 伊犁将军，途经乌鲁木齐时和诗唱和
• 莫子捷（远崖）- 济木萨知县，广东同乡，途中相遇

核心活动：
诗文唱和、同游名胜、宴集聚会、相互慰藉
    """
    
    return relationship_text

def main():
    """主函数"""
    print("正在生成颜检人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_social_network()
    
    # 绘制图形
    fig = draw_network_graph(G, characters)
    
    # 保存图片
    output_file = '颜检人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"关系网络图已保存为: {output_file}")
    
    # 显示关系说明
    relationship_info = add_relationship_annotations(G, characters)
    print(relationship_info)
    
    # 不显示图形窗口，只保存
    # plt.show()
    
    # 输出网络统计信息
    print(f"\n网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")

if __name__ == "__main__":
    main()
