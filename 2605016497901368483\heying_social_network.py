import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_heying_network():
    """创建和瑛的人物社会关系网络图"""
    
    G = nx.Graph()
    
    # 人物信息
    characters = {
        '和瑛': {
            'type': 'core', 
            'position': (0, 0), 
            'size': 5500, 
            'label': '和瑛\n(大莽/太庵)',
            'description': '蒙古镶黄旗，新疆高级官员'
        },
        '璧昌': {
            'type': 'family', 
            'position': (0, -3), 
            'size': 3500, 
            'label': '璧昌\n(和瑛子)',
            'description': '后任两江总督、福州将军'
        },
        '成书': {
            'type': 'close_colleague', 
            'position': (-3, 2), 
            'size': 4000, 
            'label': '成书\n(误庵)',
            'description': '哈密办事大臣，密友'
        },
        '松筠': {
            'type': 'high_colleague', 
            'position': (3, 2), 
            'size': 4000, 
            'label': '松筠\n(湘浦将军)',
            'description': '伊犁将军，新疆最高长官'
        },
        '达庆': {
            'type': 'high_colleague', 
            'position': (-3, -1), 
            'size': 3500, 
            'label': '达庆\n(瘦石参赞)',
            'description': '塔尔巴哈台参赞大臣'
        },
        '玉德': {
            'type': 'colleague', 
            'position': (3, -1), 
            'size': 3000, 
            'label': '玉德\n(达斋)',
            'description': '乌什办事大臣'
        },
        '常钧': {
            'type': 'literary_connection', 
            'position': (-4.5, 0), 
            'size': 2500, 
            'label': '常钧\n(常中丞)',
            'description': '前代甘肃官员，诗歌神交'
        },
        '蒋业晋': {
            'type': 'academic_influence', 
            'position': (4.5, 0), 
            'size': 2500, 
            'label': '蒋业晋',
            'description': '引用和瑛《三州辑略》'
        }
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        ('和瑛', '璧昌', {
            'weight': 5, 
            'relation': '父子关系',
            'evidence': '璧昌作者介绍："和瑛子"',
            'description': '直系血缘关系，事业传承',
            'color': '#9b59b6',
            'style': 'solid'
        }),
        ('和瑛', '成书', {
            'weight': 4, 
            'relation': '密友/诗歌唱和',
            'evidence': '《苦水驿守风简哈密成误庵侍郎》等',
            'description': '留宿衙斋，快谈三日的密友关系',
            'color': '#e74c3c',
            'style': 'solid'
        }),
        ('和瑛', '松筠', {
            'weight': 3, 
            'relation': '同僚/诗歌赠别',
            'evidence': '《寄别湘浦将军瘦石参赞四首》',
            'description': '与新疆最高长官的同僚关系',
            'color': '#3498db',
            'style': 'solid'
        }),
        ('和瑛', '达庆', {
            'weight': 3, 
            'relation': '同僚/诗歌赠别',
            'evidence': '《寄别湘浦将军瘦石参赞四首》',
            'description': '边疆大吏同事圈',
            'color': '#3498db',
            'style': 'solid'
        }),
        ('和瑛', '玉德', {
            'weight': 2, 
            'relation': '同僚/送别',
            'evidence': '《九日土鲁番送玉达斋还都》',
            'description': '回京途中相遇送别',
            'color': '#f39c12',
            'style': 'solid'
        }),
        ('和瑛', '常钧', {
            'weight': 1, 
            'relation': '跨时空诗歌唱和',
            'evidence': '《鸭子泉和常中丞原韵有序》',
            'description': '对前人诗作的神交唱和',
            'color': '#27ae60',
            'style': 'dashed'
        }),
        ('和瑛', '蒋业晋', {
            'weight': 1, 
            'relation': '学术影响/被引用',
            'evidence': '引用《三州辑略》',
            'description': '著作被后人引用考证',
            'color': '#95a5a6',
            'style': 'dotted'
        })
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_heying_graph(G, characters):
    """绘制和瑛网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(18, 14))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',              # 红色 - 核心人物
        'family': '#9b59b6',            # 紫色 - 家庭
        'close_colleague': '#3498db',   # 蓝色 - 密友同僚
        'high_colleague': '#2980b9',    # 深蓝色 - 高级同僚
        'colleague': '#f39c12',         # 橙色 - 一般同僚
        'literary_connection': '#27ae60', # 绿色 - 文学神交
        'academic_influence': '#95a5a6'  # 灰色 - 学术影响
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边 - 根据关系类型使用不同样式
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        color = data['color']
        style = data['style']
        
        # 绘制边的光晕效果
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2.5,
                              alpha=0.3, 
                              edge_color=color,
                              style=style)
        
        # 绘制主边
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 1.5,
                              alpha=0.8, 
                              edge_color=color,
                              style=style)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        # 绘制节点光晕
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size + 400,
                             alpha=0.3)
        
        # 绘制主节点
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=4)
    
    # 添加标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小和颜色
        if data['type'] == 'core':
            fontsize = 14
            bbox_color = '#2c3e50'
        elif data['type'] in ['close_colleague', 'high_colleague']:
            fontsize = 11
            bbox_color = '#34495e'
        else:
            fontsize = 10
            bbox_color = '#34495e'
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.4", 
                            facecolor=bbox_color, 
                            alpha=0.7,
                            edgecolor='none'))
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物（和瑛）', alpha=0.9),
        mpatches.Patch(color=node_colors['family'], label='家庭成员（璧昌）', alpha=0.9),
        mpatches.Patch(color=node_colors['close_colleague'], label='密友同僚（成书）', alpha=0.9),
        mpatches.Patch(color=node_colors['high_colleague'], label='高级同僚（松筠/达庆）', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='一般同僚（玉德）', alpha=0.9),
        mpatches.Patch(color=node_colors['literary_connection'], label='文学神交（常钧）', alpha=0.9),
        mpatches.Patch(color=node_colors['academic_influence'], label='学术影响（蒋业晋）', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=10,
             title='人物关系类型',
             title_fontsize=11)
    
    # 标题
    ax.text(0.5, 0.95, "和瑛人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '嘉庆七年至十三年 新疆任职期间 (1802-1808)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-6, 6)
    ax.set_ylim(-4, 4)
    
    plt.tight_layout()
    return fig

def add_network_analysis():
    """添加网络分析"""
    
    analysis = {
        "网络特点": [
            "• 多层次关系：家庭、密友、同僚、文学、学术等多个层面",
            "• 时空跨越：既有同时代关系，也有跨时空的文学神交",
            "• 等级分明：从密友到一般同僚，体现了清代官场等级",
            "• 文学纽带：诗歌唱和是重要的社交方式",
            "• 学术影响：著作《三州辑略》体现了学者身份"
        ],
        
        "关系圈分析": {
            "核心家庭圈": [
                "• 璧昌：父子血缘关系，事业传承",
                "• 体现了清代官宦世家的特点",
                "• 边疆事业的家族延续性"
            ],
            "核心同事圈": [
                "• 成书：哈密办事大臣，留宿快谈的密友",
                "• 松筠：伊犁将军，新疆最高长官",
                "• 达庆：塔尔巴哈台参赞大臣",
                "• 构成了新疆高级官员的核心圈子"
            ],
            "一般同僚圈": [
                "• 玉德：乌什办事大臣，回京途中相遇",
                "• 体现了官员流动中的偶然交往"
            ],
            "文学神交圈": [
                "• 常钧：前代甘肃官员，通过诗歌神交",
                "• 体现了文人跨时空的精神交流"
            ],
            "学术影响圈": [
                "• 蒋业晋：引用《三州辑略》进行考证",
                "• 体现了和瑛的学术贡献和影响力"
            ]
        }
    }
    
    return analysis

def main():
    """主函数"""
    print("正在创建和瑛人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_heying_network()
    
    # 绘制图形
    fig = draw_heying_graph(G, characters)
    
    # 保存图片
    output_file = '和瑛人物社会关系网络图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 和瑛网络图已保存为: {output_file}")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
        print(f"  证据: {data['evidence']}")
        print(f"  描述: {data['description']}")
        print()
    
    # 输出详细分析
    analysis = add_network_analysis()
    print("📋 网络关系深度分析:")
    for category, content in analysis.items():
        print(f"\n{category}:")
        if isinstance(content, list):
            for item in content:
                print(f"  {item}")
        elif isinstance(content, dict):
            for circle, details in content.items():
                print(f"  {circle}:")
                for detail in details:
                    print(f"    {detail}")
    
    print("\n" + "=" * 80)
    print("📖 总结:")
    print("   和瑛的社交网络展现了清代高级边疆官员的典型特征：")
    print("   既有严密的官场等级关系，又有深厚的文人情谊；")
    print("   既承担着边疆治理的重任，又保持着学者的文化追求。")
    print("   他的网络跨越时空，体现了文人官员的精神世界和社会责任。")
    print("=" * 80)

if __name__ == "__main__":
    main()
